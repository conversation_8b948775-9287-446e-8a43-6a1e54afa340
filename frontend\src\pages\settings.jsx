import React, { useState, useEffect } from "react";

const SettingsForm = () => {
  // State for card expansion
  const [expandedCard, setExpandedCard] = useState(null);
  // State for minus stock billing
  const [allowMinusStockBilling, setAllowMinusStockBilling] = useState(false);
  const [productSearchStartsWithOnly, setProductSearchStartsWithOnly] = useState(false);
  const [autoAddScannedProductToCart, setAutoAddScannedProductToCart] = useState(false);

  // Load from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem("allowMinusStockBilling");
    if (saved !== null) {
      setAllowMinusStockBilling(saved === "true");
    }
    const savedSearchMode = localStorage.getItem("productSearchStartsWithOnly");
    if (savedSearchMode !== null) {
      setProductSearchStartsWithOnly(savedSearchMode === "true");
    }
    const savedAutoAdd = localStorage.getItem("autoAddScannedProductToCart");
    if (savedAutoAdd !== null) {
      setAutoAddScannedProductToCart(savedAutoAdd === "true");
    }
  }, []);

  // Save to localStorage when changed
  useEffect(() => {
    localStorage.setItem("allowMinusStockBilling", allowMinusStockBilling);
  }, [allowMinusStockBilling]);
  useEffect(() => {
    localStorage.setItem("productSearchStartsWithOnly", productSearchStartsWithOnly);
  }, [productSearchStartsWithOnly]);
  useEffect(() => {
    localStorage.setItem("autoAddScannedProductToCart", autoAddScannedProductToCart);
  }, [autoAddScannedProductToCart]);

  // Card click handler
  const handleCardClick = (card) => {
    setExpandedCard(expandedCard === card ? null : card);
  };

  return (
    <div className="max-w-2xl mx-auto py-8">
      <h2 className="text-2xl font-bold mb-6 text-center">Settings</h2>
      <div className="grid gap-6">
        {/* Touch POS Settings Card */}
        <div
          className={`rounded-lg shadow-lg border border-gray-200 bg-white transition-all cursor-pointer ${expandedCard === "touchpos" ? "ring-2 ring-blue-400" : "hover:shadow-xl"}`}
          onClick={() => handleCardClick("touchpos")}
        >
          <div className="flex items-center justify-between p-5">
            <div>
              <h3 className="text-lg font-semibold">Touch POS Settings</h3>
              <p className="text-sm text-gray-500">Configure Touch POS behavior</p>
            </div>
            <span className="ml-4 text-blue-500">{expandedCard === "touchpos" ? "▲" : "▼"}</span>
          </div>
          {expandedCard === "touchpos" && (
            <div className="border-t px-5 py-4 flex items-center justify-between">
              <div>
                <div className="font-medium">Enable Minus Stock Billing</div>
                <div className="text-xs text-gray-500">Allow billing even if stock goes negative</div>
              </div>
              <button
                onClick={e => { e.stopPropagation(); setAllowMinusStockBilling(v => !v); }}
                className={`ml-4 w-14 h-8 flex items-center rounded-full p-1 transition-colors duration-300 ${allowMinusStockBilling ? "bg-green-500" : "bg-gray-300"}`}
                aria-pressed={allowMinusStockBilling}
              >
                <span
                  className={`inline-block w-6 h-6 transform bg-white rounded-full shadow transition-transform duration-300 ${allowMinusStockBilling ? "translate-x-6" : "translate-x-0"}`}
                />
              </button>
              <span className={`ml-2 text-sm font-bold ${allowMinusStockBilling ? "text-green-600" : "text-gray-500"}`}>{allowMinusStockBilling ? "ON" : "OFF"}</span>
            </div>
          )}
        </div>
        {/* Product Search Settings Card */}
        <div
          className={`rounded-lg shadow-lg border border-gray-200 bg-white transition-all cursor-pointer ${expandedCard === "searchmode" ? "ring-2 ring-blue-400" : "hover:shadow-xl"}`}
          onClick={() => handleCardClick("searchmode")}
        >
          <div className="flex items-center justify-between p-5">
            <div>
              <h3 className="text-lg font-semibold">Product Search Settings</h3>
              <p className="text-sm text-gray-500">Configure product search behavior</p>
            </div>
            <span className="ml-4 text-blue-500">{expandedCard === "searchmode" ? "▲" : "▼"}</span>
          </div>
          {expandedCard === "searchmode" && (
            <div className="border-t px-5 py-4 flex items-center justify-between">
              <div>
                <div className="font-medium">Product Search: Starts With Only</div>
                <div className="text-xs text-gray-500">If ON, product search will only match names starting with your input (not anywhere in the name)</div>
              </div>
              <button
                onClick={e => { e.stopPropagation(); setProductSearchStartsWithOnly(v => !v); }}
                className={`ml-4 w-14 h-8 flex items-center rounded-full p-1 transition-colors duration-300 ${productSearchStartsWithOnly ? "bg-green-500" : "bg-gray-300"}`}
                aria-pressed={productSearchStartsWithOnly}
              >
                <span
                  className={`inline-block w-6 h-6 transform bg-white rounded-full shadow transition-transform duration-300 ${productSearchStartsWithOnly ? "translate-x-6" : "translate-x-0"}`}
                />
              </button>
              <span className={`ml-2 text-sm font-bold ${productSearchStartsWithOnly ? "text-green-600" : "text-gray-500"}`}>{productSearchStartsWithOnly ? "ON" : "OFF"}</span>
            </div>
          )}
        </div>
        {/* Auto-add Scanned Product Card */}
        <div
          className={`rounded-lg shadow-lg border border-gray-200 bg-white transition-all cursor-pointer ${expandedCard === "autoaddscan" ? "ring-2 ring-blue-400" : "hover:shadow-xl"}`}
          onClick={() => handleCardClick("autoaddscan")}
        >
          <div className="flex items-center justify-between p-5">
            <div>
              <h3 className="text-lg font-semibold">Barcode Scan Behavior</h3>
              <p className="text-sm text-gray-500">Control what happens when you scan a barcode in POS</p>
            </div>
            <span className="ml-4 text-blue-500">{expandedCard === "autoaddscan" ? "▲" : "▼"}</span>
          </div>
          {expandedCard === "autoaddscan" && (
            <div className="border-t px-5 py-4 flex items-center justify-between">
              <div>
                <div className="font-medium">Auto-add scanned product to cart</div>
                <div className="text-xs text-gray-500">If ON, scanning a barcode will immediately add the product to the cart/item list. If OFF, it will only select the product in the search bar.</div>
              </div>
              <button
                onClick={e => { e.stopPropagation(); setAutoAddScannedProductToCart(v => !v); }}
                className={`ml-4 w-14 h-8 flex items-center rounded-full p-1 transition-colors duration-300 ${autoAddScannedProductToCart ? "bg-green-500" : "bg-gray-300"}`}
                aria-pressed={autoAddScannedProductToCart}
              >
                <span
                  className={`inline-block w-6 h-6 transform bg-white rounded-full shadow transition-transform duration-300 ${autoAddScannedProductToCart ? "translate-x-6" : "translate-x-0"}`}
                />
              </button>
              <span className={`ml-2 text-sm font-bold ${autoAddScannedProductToCart ? "text-green-600" : "text-gray-500"}`}>{autoAddScannedProductToCart ? "ON" : "OFF"}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsForm;

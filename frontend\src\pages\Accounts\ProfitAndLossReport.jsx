import React, { useState, useRef } from 'react';
import { FaPrint, FaFileExcel, FaFileImport } from 'react-icons/fa';
import { useAuth } from '../../context/NewAuthContext';
import * as XLSX from 'xlsx';
import axios from 'axios';

export const ProfitAndLossReport = () => {
  const { user } = useAuth();
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  
  // Set default from date to January 1st of current year
  const defaultFromDate = `${currentYear}-01-01`;
  // Set default to date to current date
  const defaultToDate = currentDate.toISOString().split('T')[0];

  const [fromDate, setFromDate] = useState(defaultFromDate);
  const [toDate, setToDate] = useState(defaultToDate);
  const [reportData, setReportData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);

  // Default totals
  const defaultTotals = {
    totalIncome: 0,
    totalExpenses: 0,
    netProfitLoss: 0
  };

  const [totals, setTotals] = useState(defaultTotals);
  const [expandedIncomeCategories, setExpandedIncomeCategories] = useState([]);
  const [expandedExpenseCategories, setExpandedExpenseCategories] = useState([]);

  // Helper to group by category
  const groupByCategory = (arr) => {
    return arr.reduce((acc, item) => {
      if (!acc[item.category]) acc[item.category] = [];
      acc[item.category].push(item);
      return acc;
    }, {});
  };

  const groupedIncome = reportData ? groupByCategory(reportData.income) : {};
  const groupedExpenses = reportData ? groupByCategory(reportData.expenses) : {};

  const handleToggleIncomeCategory = (category) => {
    setExpandedIncomeCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };
  const handleToggleExpenseCategory = (category) => {
    setExpandedExpenseCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  const handleGenerateReport = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await axios.post('/api/profit-and-loss', {
        from_date: fromDate,
        to_date: toDate,
      });
      
      if (response.data.success) {
        setReportData(response.data.data);
        setTotals(response.data.totals);
      } else {
        throw new Error(response.data.message);
      }
    } catch (err) {
      setError('Failed to generate report. Please try again.');
      console.error('Error generating report:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    if (!reportData) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>Profit and Loss Report - ${fromDate} to ${toDate}</title>
          <style>
            @page {
              size: A4 portrait;
              margin: 10mm;
            }
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              color: #333;
            }
            .print-header {
              text-align: center;
              margin-bottom: 20px;
            }
            .print-header h1 {
              font-size: 24px;
              margin: 0;
              color: #111;
            }
            .print-header p {
              margin: 5px 0;
              font-size: 14px;
            }
            .print-summary {
              display: flex;
              justify-content: space-between;
              margin-bottom: 20px;
            }
            .print-summary-item {
              flex: 1;
              padding: 10px;
              border: 1px solid #ddd;
              margin: 0 5px;
              text-align: center;
            }
            .print-summary-item h3 {
              margin: 0 0 5px 0;
              font-size: 16px;
            }
            .print-summary-item p {
              margin: 0;
              font-size: 18px;
              font-weight: bold;
            }
            .print-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 20px;
            }
            .print-table th, .print-table td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            .print-table th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            .print-table .section-header {
              background-color: #eaeaea;
              font-weight: bold;
            }
            .print-table .total-row {
              background-color: #f5f5f5;
              font-weight: bold;
            }
            .print-table .net-row {
              background-color: #e6f7ff;
              font-weight: bold;
            }
            .print-footer {
              margin-top: 30px;
              font-size: 12px;
              text-align: center;
              color: #666;
            }
            .text-green {
              color: #28a745;
            }
            .text-red {
              color: #dc3545;
            }
            .text-blue {
              color: #007bff;
            }
            .text-orange {
              color: #fd7e14;
            }
            @media print {
              .no-print {
                display: none !important;
              }
            }
          </style>
        </head>
        <body>
          <div class="print-header">
            <h1>Profit and Loss Report</h1>
            <p>Date Range: ${fromDate} to ${toDate}</p>
            <p>Generated on: ${new Date().toLocaleDateString()} by ${user?.name || 'System'}</p>
          </div>
          
          <div class="print-summary">
            <div class="print-summary-item">
              <h3>Total Income</h3>
              <p class="text-green">LKR ${totals.totalIncome.toLocaleString()}</p>
            </div>
            <div class="print-summary-item">
              <h3>Total Expenses</h3>
              <p class="text-red">LKR ${totals.totalExpenses.toLocaleString()}</p>
            </div>
            <div class="print-summary-item">
              <h3>Net Profit/Loss</h3>
              <p class="${totals.netProfitLoss >= 0 ? 'text-blue' : 'text-orange'}">
                LKR ${Math.abs(totals.netProfitLoss).toLocaleString()} ${totals.netProfitLoss >= 0 ? '(Profit)' : '(Loss)'}
              </p>
            </div>
          </div>
          
          <table class="print-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Category</th>
                <th>Description</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr class="section-header">
                <td colspan="4">Income</td>
              </tr>
              ${reportData.income.map(item => `
                <tr>
                  <td>${item.date}</td>
                  <td>${item.category}</td>
                  <td>${item.description}</td>
                  <td class="text-green">LKR ${item.amount.toLocaleString()}</td>
                </tr>
              `).join('')}
              <tr class="total-row">
                <td colspan="3" style="text-align: right;">Total Income</td>
                <td class="text-green">LKR ${totals.totalIncome.toLocaleString()}</td>
              </tr>
              
              <tr class="section-header">
                <td colspan="4">Expenses</td>
              </tr>
              ${reportData.expenses.map(item => `
                <tr>
                  <td>${item.date}</td>
                  <td>${item.category}</td>
                  <td>${item.description}</td>
                  <td class="text-red">LKR ${item.amount.toLocaleString()}</td>
                </tr>
              `).join('')}
              <tr class="total-row">
                <td colspan="3" style="text-align: right;">Total Expenses</td>
                <td class="text-red">LKR ${totals.totalExpenses.toLocaleString()}</td>
              </tr>
              
              <tr class="net-row">
                <td colspan="3" style="text-align: right;">Net Profit/Loss</td>
                <td class="${totals.netProfitLoss >= 0 ? 'text-blue' : 'text-orange'}">
                  LKR ${Math.abs(totals.netProfitLoss).toLocaleString()} ${totals.netProfitLoss >= 0 ? '(Profit)' : '(Loss)'}
                </td>
              </tr>
            </tbody>
          </table>
          
          <div class="print-footer">
            <p>© ${new Date().getFullYear()} Your Company Name. All rights reserved.</p>
          </div>
          
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
                window.close();
              }, 200);
            };
          </script>
        </body>
      </html>
    `);
    printWindow.document.close();
  };

  const handleExport = (type) => {
    if (!reportData) return;

    try {
      const exportData = [
        ['Date', 'Category', 'Description', 'Amount', 'Type'],
        ...reportData.income.map(item => [item.date, item.category, item.description, item.amount, 'Income']),
        ...reportData.expenses.map(item => [item.date, item.category, item.description, item.amount, 'Expense']),
        ['', '', 'Total Income', totals.totalIncome, ''],
        ['', '', 'Total Expenses', totals.totalExpenses, ''],
        ['', '', 'Net Profit/Loss', totals.netProfitLoss, totals.netProfitLoss >= 0 ? 'Profit' : 'Loss']
      ];

      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.aoa_to_sheet(exportData);
      XLSX.utils.book_append_sheet(wb, ws, "ProfitAndLoss");

      if (type === 'excel') {
        XLSX.writeFile(wb, `ProfitAndLoss_${fromDate}_to_${toDate}.xlsx`);
      }
    } catch (err) {
      setError('Failed to export report. Please try again.');
      console.error('Export error:', err);
    }
  };

  const handleImport = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

        if (jsonData.length > 0) {
          const headers = jsonData[0];
          const rows = jsonData.slice(1);

          if (headers.length < 4) {
            throw new Error('Invalid file format');
          }

          const importedData = { income: [], expenses: [] };
          
          rows.forEach(row => {
            if (row.length >= 4) {
              const type = row[4] || '';
              const item = {
                date: row[0] || '',
                category: row[1] || '',
                description: row[2] || '',
                amount: Number(row[3]) || 0
              };
              
              if (type.toLowerCase() === 'income') {
                importedData.income.push(item);
              } else if (type.toLowerCase() === 'expense') {
                importedData.expenses.push(item);
              }
            }
          });

          const totalIncome = importedData.income.reduce((sum, item) => sum + item.amount, 0);
          const totalExpenses = importedData.expenses.reduce((sum, item) => sum + item.amount, 0);
          const netProfitLoss = totalIncome - totalExpenses;

          setReportData(importedData);
          setTotals({ totalIncome, totalExpenses, netProfitLoss });
        }
      } catch (err) {
        setError('Failed to import file. Please check the file format.');
        console.error('Import error:', err);
      }
    };
    reader.readAsArrayBuffer(file);
  };

  const triggerImport = () => {
    fileInputRef.current.click();
  };

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <div className="mx-auto max-w-7xl">
        <h1 className="mb-6 text-2xl font-bold text-gray-800">Profit and Loss Report</h1>
        
        <div className="p-6 mb-6 bg-white rounded-lg shadow-md">
          <div className="grid grid-cols-1 gap-4 mb-4 md:grid-cols-3">
            <div>
              <label className="block mb-1 text-sm font-medium text-gray-700">From Date</label>
              <input
                type="date"
                value={fromDate}
                onChange={(e) => setFromDate(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block mb-1 text-sm font-medium text-gray-700">To Date</label>
              <input
                type="date"
                value={toDate}
                onChange={(e) => setToDate(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={handleGenerateReport}
                disabled={loading || !fromDate || !toDate}
                className={`w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md flex items-center justify-center ${(loading || !fromDate || !toDate) ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {loading ? (
                  <>
                    <svg className="w-5 h-5 mr-3 -ml-1 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </>
                ) : (
                  <>
                    Generate Report
                  </>
                )}
              </button>
            </div>
          </div>
          
          {error && (
            <div className="p-3 mt-4 text-red-700 bg-red-100 rounded-md">
              {error}
            </div>
          )}
        </div>
        
        {reportData ? (
          <>
            <div className="grid grid-cols-1 gap-6 mb-6 md:grid-cols-3">
              <div className="p-6 bg-white border-l-4 border-green-500 rounded-lg shadow-md">
                <h3 className="mb-2 text-lg font-medium text-gray-700">Total Income</h3>
                <p className="text-2xl font-bold text-green-600">LKR {totals.totalIncome.toLocaleString()}</p>
              </div>
              <div className="p-6 bg-white border-l-4 border-red-500 rounded-lg shadow-md">
                <h3 className="mb-2 text-lg font-medium text-gray-700">Total Expenses</h3>
                <p className="text-2xl font-bold text-red-600">LKR {totals.totalExpenses.toLocaleString()}</p>
              </div>
              <div className={`bg-white p-6 rounded-lg shadow-md border-l-4 ${totals.netProfitLoss >= 0 ? 'border-blue-500' : 'border-orange-500'}`}>
                <h3 className="mb-2 text-lg font-medium text-gray-700">Net Profit/Loss</h3>
                <p className={`text-2xl font-bold ${totals.netProfitLoss >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
                  LKR {Math.abs(totals.netProfitLoss).toLocaleString()} {totals.netProfitLoss >= 0 ? '(Profit)' : '(Loss)'}
                </p>
              </div>
            </div>
            
            <div className="flex justify-end p-4 mb-6 space-x-3 bg-white rounded-lg shadow-md">
              <button
                onClick={handlePrint}
                className="flex items-center px-4 py-2 font-medium text-gray-800 bg-gray-200 rounded-md hover:bg-gray-300"
              >
                <FaPrint className="mr-2" />
                Print
              </button>
              <button
                onClick={() => handleExport('excel')}
                className="flex items-center px-4 py-2 font-medium text-green-800 bg-green-200 rounded-md hover:bg-green-300"
              >
                <FaFileExcel className="mr-2" />
                Export Excel
              </button>
              <button
                onClick={triggerImport}
                className="flex items-center px-4 py-2 font-medium text-blue-800 bg-blue-200 rounded-md hover:bg-blue-300"
              >
                <FaFileImport className="mr-2" />
                Import Excel
              </button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleImport}
                accept=".xlsx, .xls"
                className="hidden"
              />
            </div>
            
            <div className="no-print">
              <div className="p-6 bg-white rounded-lg shadow-md">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Date</th>
                        <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Category</th>
                        <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Description</th>
                        <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Amount</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr className="bg-gray-100">
                        <td colSpan="4" className="px-6 py-3 font-bold text-gray-700">Income</td>
                      </tr>
                      {Object.entries(groupedIncome).map(([category, items], idx) => {
                        const total = items.reduce((sum, item) => sum + item.amount, 0);
                        const expanded = expandedIncomeCategories.includes(category);
                        return (
                          <React.Fragment key={`income-cat-${category}`}> 
                            <tr className="bg-green-50 cursor-pointer hover:bg-green-100" onClick={() => handleToggleIncomeCategory(category)}>
                              <td className="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">{expanded ? '' : '-'}</td>
                              <td className="px-6 py-4 text-sm font-bold text-gray-900 whitespace-nowrap">{category}</td>
                              <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{expanded ? 'Click to hide details' : 'Click to view details'}</td>
                              <td className="px-6 py-4 text-sm font-bold text-green-700 whitespace-nowrap">LKR {total.toLocaleString()}</td>
                            </tr>
                            {expanded && items.map((item, i) => (
                              <tr key={`income-${category}-${i}`} className="bg-white">
                                <td className="px-6 py-4 text-xs text-gray-500 whitespace-nowrap">{new Date(item.date).toLocaleDateString()}</td>
                                <td className="px-6 py-4 text-xs text-gray-500 whitespace-nowrap"></td>
                                <td className="px-6 py-4 text-xs text-gray-500 whitespace-nowrap">{item.description}</td>
                                <td className="px-6 py-4 text-xs text-green-600 whitespace-nowrap">LKR {item.amount.toLocaleString()}</td>
                              </tr>
                            ))}
                          </React.Fragment>
                        );
                      })}
                      <tr className="bg-gray-50">
                        <td colSpan="3" className="px-6 py-3 font-bold text-right text-gray-700">Total Income</td>
                        <td className="px-6 py-3 text-sm font-bold text-green-600 whitespace-nowrap">LKR {totals.totalIncome.toLocaleString()}</td>
                      </tr>
                      <tr className="bg-gray-100">
                        <td colSpan="4" className="px-6 py-3 font-bold text-gray-700">Expenses</td>
                      </tr>
                      {Object.entries(groupedExpenses).map(([category, items], idx) => {
                        const total = items.reduce((sum, item) => sum + item.amount, 0);
                        const expanded = expandedExpenseCategories.includes(category);
                        return (
                          <React.Fragment key={`expense-cat-${category}`}> 
                            <tr className="bg-red-50 cursor-pointer hover:bg-red-100" onClick={() => handleToggleExpenseCategory(category)}>
                              <td className="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">{expanded ? '' : '-'}</td>
                              <td className="px-6 py-4 text-sm font-bold text-gray-900 whitespace-nowrap">{category}</td>
                              <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">{expanded ? 'Click to hide details' : 'Click to view details'}</td>
                              <td className="px-6 py-4 text-sm font-bold text-red-700 whitespace-nowrap">LKR {total.toLocaleString()}</td>
                            </tr>
                            {expanded && items.map((item, i) => (
                              <tr key={`expense-${category}-${i}`} className="bg-white">
                                <td className="px-6 py-4 text-xs text-gray-500 whitespace-nowrap">{new Date(item.date).toLocaleDateString()}</td>
                                <td className="px-6 py-4 text-xs text-gray-500 whitespace-nowrap"></td>
                                <td className="px-6 py-4 text-xs text-gray-500 whitespace-nowrap">{item.description}</td>
                                <td className="px-6 py-4 text-xs text-red-600 whitespace-nowrap">LKR {item.amount.toLocaleString()}</td>
                              </tr>
                            ))}
                          </React.Fragment>
                        );
                      })}
                      <tr className="bg-gray-50">
                        <td colSpan="3" className="px-6 py-3 font-bold text-right text-gray-700">Total Expenses</td>
                        <td className="px-6 py-3 text-sm font-bold text-red-600 whitespace-nowrap">LKR {totals.totalExpenses.toLocaleString()}</td>
                      </tr>
                      <tr className="bg-blue-50">
                        <td colSpan="3" className="px-6 py-3 font-bold text-right text-gray-700">Net Profit/Loss</td>
                        <td className={`px-6 py-3 whitespace-nowrap text-sm font-bold ${totals.netProfitLoss >= 0 ? 'text-blue-600' : 'text-orange-600'}`}>
                          LKR {Math.abs(totals.netProfitLoss).toLocaleString()} {totals.netProfitLoss >= 0 ? '(Profit)' : '(Loss)'}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className="flex items-center justify-between pt-4 mt-6 border-t border-gray-200">
                  <div className="text-sm text-gray-500">
                    Generated on: {new Date().toLocaleDateString()} by {user?.name || 'System'}
                  </div>
                  <div className="text-sm text-gray-500">
                    Date Range: {fromDate} to {toDate}
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="p-6 text-center bg-white rounded-lg shadow-md">
            <p className="text-gray-600">Click "Generate Report" to view the profit and loss statement</p>
          </div>
        )}
      </div>
    </div>
  );
};